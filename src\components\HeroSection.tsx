import { But<PERSON> } from "@/components/ui/button";
import { Phone, Calendar, MapPin, Clock, Shield, Star } from "lucide-react";

interface HeroSectionProps {
  onCall: () => void;
  onReservation: () => void;
}

export const HeroSection = ({ onCall, onReservation }: HeroSectionProps) => {
  return (
    <section id="home" className="relative h-screen flex items-center justify-center bg-gradient-to-br from-primary/90 to-primary/70 text-white overflow-hidden">
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1540541338287-41700207dee6?q=80&w=2070')] bg-cover bg-center"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-primary/80 to-primary/60"></div>
      
      <div className="relative z-10 text-center px-4 max-w-4xl mx-auto animate-fade-in">
        <div className="mb-6 animate-slide-up">
          <Star className="h-12 w-12 text-yellow-400 mx-auto mb-4 animate-pulse" />
          <span className="bg-yellow-400 text-primary px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
            ★★★★★ Premium Termal Otel
          </span>
        </div>
        
        <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight drop-shadow-lg animate-slide-in-left animate-delay-200">
          Akasya Vadisi Termal Otel
        </h1>
        
        <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto leading-relaxed drop-shadow-md animate-slide-in-right animate-delay-300">
          Ankara Kızılcahamam'da doğayla iç içe, 124 oda ile alkolsüz herşey dahil konseptinde 
          sağlık ve huzur dolu termal tatil deneyimi
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8 animate-slide-up animate-delay-500">
          <Button size="lg" variant="secondary" onClick={onCall} className="text-lg px-8 py-4 shadow-xl hover:shadow-2xl hover:scale-105 transition-all">
            <Phone className="mr-2 h-5 w-5" />
            Hemen Ara
          </Button>
          <Button 
            size="lg" 
            variant="outline" 
            className="bg-white/10 text-white border-white hover:bg-white hover:text-primary text-lg px-8 py-4 shadow-xl hover:shadow-2xl hover:scale-105 transition-all" 
            onClick={onReservation}
          >
            <Calendar className="mr-2 h-5 w-5" />
            Rezervasyon Yap
          </Button>
        </div>
        
        <div className="flex items-center justify-center space-x-8 text-sm animate-fade-in animate-delay-700">
          <div className="flex items-center bg-white/10 px-3 py-2 rounded-lg backdrop-blur-sm">
            <MapPin className="h-4 w-4 mr-1" />
            Ankara'ya 40 dk
          </div>
          <div className="flex items-center bg-white/10 px-3 py-2 rounded-lg backdrop-blur-sm">
            <Clock className="h-4 w-4 mr-1" />
            7/24 Açık
          </div>
          <div className="flex items-center bg-white/10 px-3 py-2 rounded-lg backdrop-blur-sm">
            <Shield className="h-4 w-4 mr-1" />
            Alkolsüz Tesis
          </div>
        </div>
      </div>
    </section>
  );
};