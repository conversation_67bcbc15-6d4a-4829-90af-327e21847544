import { Button } from "@/components/ui/button";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";

const galleryImages = [
  // <PERSON><PERSON>örüntüler
  {
    url: "/images/genel/photo-15215-3EeHV1ZGzgWdvhI.jpg",
    title: "Ana Bina",
    description: "Modern mimari ve doğal güzellik"
  },
  {
    url: "/images/genel/photo-15215-5Vp32PfYF1JTMZt.jpg",
    title: "<PERSON><PERSON><PERSON> Çevre",
    description: "Yeşillikler içinde huzur"
  },
  {
    url: "/images/genel/photo-15215-B74OHsUKA9omMNg.jpg",
    title: "Otel Genel Görünüm",
    description: "<PERSON>ü<PERSON> ve konfor bir arada"
  },
  {
    url: "/images/genel/photo-15215-BEIlcSsLG9fidQ1.jpg",
    title: "Otel Bahçesi",
    description: "<PERSON><PERSON><PERSON><PERSON> iç içe tasarım"
  },
  {
    url: "/images/genel/photo-15215-INmgwzDQqdJpKF3.jpg",
    title: "Otel Girişi",
    description: "Misafirlerimizi karşılayan güzellik"
  },
  {
    url: "/images/genel/photo-15215-P7pWlAzekRq4BFb.jpg",
    title: "Peyzaj Alanları",
    description: "Özenle tasarlanmış bahçeler"
  },
  {
    url: "/images/genel/photo-15215-VxuQ4KdSP0HEbM9.jpg",
    title: "Doğa Manzarası",
    description: "Nefes kesen doğal güzellik"
  },
  {
    url: "/images/genel/photo-15215-aWEG3p4eKxFPsnu.jpg",
    title: "Otel Kompleksi",
    description: "Geniş ve ferah alanlar"
  },
  {
    url: "/images/genel/photo-15215-d4e1APz92hFpE6B.jpg",
    title: "Dinlendirici Ortam",
    description: "Huzur dolu anlar"
  },
  {
    url: "/images/genel/photo-15215-iDWfMOkRKA4pY52.jpg",
    title: "Otel Alanları",
    description: "Her detayda kalite"
  },
  {
    url: "/images/genel/photo-15215-vAybMCu3ceRfd2B.jpg",
    title: "Yeşil Alanlar",
    description: "Doğanın kalbinde konaklama"
  },
  {
    url: "/images/genel/photo-15215-ztPypsoX4BFdgC0.jpg",
    title: "Otel Panorama",
    description: "Büyüleyici genel görünüm"
  },

  // Havuz Alanları
  {
    url: "/images/havuzlar/havuz-1.jpg",
    title: "Ana Termal Havuz",
    description: "Şifalı doğal termal sular"
  },
  {
    url: "/images/havuzlar/havuz-2.jpg",
    title: "Açık Havuz",
    description: "Doğayla iç içe yüzme keyfi"
  },
  {
    url: "/images/havuzlar/havuz-3.jpg",
    title: "Termal Havuz Alanı",
    description: "Sağlık ve huzur merkezi"
  },
  {
    url: "/images/havuzlar/havuz-4.jpg",
    title: "Havuz Kompleksi",
    description: "Modern havuz tesisleri"
  },

  // Odalar
  {
    url: "/images/odalar/oda-1.jpg",
    title: "Standart Oda",
    description: "Konforlu konaklama"
  },
  {
    url: "/images/odalar/oda-2.jpg",
    title: "Oda İç Mekan",
    description: "Modern ve şık tasarım"
  },
  {
    url: "/images/odalar/oda-3.jpg",
    title: "Yatak Odası",
    description: "Rahat ve huzurlu uyku"
  },
  {
    url: "/images/odalar/oda-4.jpg",
    title: "Oda Detayları",
    description: "Kaliteli mobilyalar"
  },
  {
    url: "/images/odalar/oda-6.jpg",
    title: "Aile Odası",
    description: "Geniş ve ferah alanlar"
  },
  {
    url: "/images/odalar/oda-8.jpg",
    title: "Lüks Oda",
    description: "Premium konaklama deneyimi"
  },
  {
    url: "/images/odalar/oda-9.jpg",
    title: "Oda Konforu",
    description: "Her detayda kalite"
  },
  {
    url: "/images/odalar/oda-10.jpeg",
    title: "VIP Oda",
    description: "Özel tasarım ve konfor"
  },
  {
    url: "/images/odalar/photo-15215-EFgT1XqVS6b5t7G.jpg",
    title: "Oda Manzarası",
    description: "Doğa manzaralı odalar"
  },

  // Restoran
  {
    url: "/images/restoran/restoran-1.jpg",
    title: "Ana Restoran",
    description: "Lezzetli ve sağlıklı yemekler"
  },
  {
    url: "/images/restoran/restoran-2.jpg",
    title: "Yemek Salonu",
    description: "Geniş ve ferah yemek alanı"
  },
  {
    url: "/images/restoran/restoran-3.jpg",
    title: "Restoran Terası",
    description: "Açık hava yemek keyfi"
  },

  // Spa & Sauna
  {
    url: "/images/spasauna/spa-1.jpg",
    title: "Spa Merkezi",
    description: "Huzur ve sağlık merkezi"
  },
  {
    url: "/images/spasauna/spa-2.jpg",
    title: "Masaj Odaları",
    description: "Profesyonel spa hizmetleri"
  },
  {
    url: "/images/spasauna/spa-3.jpg",
    title: "Sauna Alanı",
    description: "Geleneksel sauna deneyimi"
  },
  {
    url: "/images/spasauna/spa-4.jpg",
    title: "Wellness Merkezi",
    description: "Sağlık ve güzellik bir arada"
  }
];

export const GallerySection = () => {
  return (
    <section id="gallery" className="py-20 px-4 bg-gradient-to-b from-secondary/30 to-background">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <span className="text-primary font-semibold text-sm uppercase tracking-wider animate-slide-up">Galeri</span>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 mt-2 animate-slide-in-left animate-delay-200">Tesisimizden Görüntüler</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto animate-slide-in-right animate-delay-300">
            Doğayla iç içe termal otelimizin güzelliklerini keşfedin
          </p>
        </div>

        {/* Image carousel */}
        <div className="mb-12 animate-slide-in-left animate-delay-700">
          <Carousel className="w-full">
            <CarouselContent className="-ml-2 md:-ml-4">
              {galleryImages.map((image, index) => (
                <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                  <div className="relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group">
                    <img
                      src={image.url}
                      alt={image.title}
                      className="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-4 left-4 text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                        <p className="font-semibold text-lg">{image.title}</p>
                        <p className="text-sm opacity-90">{image.description}</p>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg" />
            <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg" />
          </Carousel>
        </div>

        {/* Grid layout for remaining images */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {galleryImages.slice(0, 6).map((image, index) => (
            <div key={index} className="relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group">
              <img
                src={image.url}
                alt={image.title}
                className="w-full h-64 object-cover transform group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 text-white">
                  <p className="font-semibold">{image.title}</p>
                  <p className="text-sm opacity-90">{image.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Button size="lg" variant="outline" className="hover:bg-primary hover:text-white transition-all">
            Tüm Fotoğrafları Gör
          </Button>
        </div>
      </div>
    </section>
  );
};