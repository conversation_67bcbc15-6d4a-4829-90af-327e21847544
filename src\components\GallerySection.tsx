import { Button } from "@/components/ui/button";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";

const galleryImages = [
  {
    url: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=1000",
    title: "Ana Bina",
    description: "Modern mimari ve doğal güzellik"
  },
  {
    url: "https://images.unsplash.com/photo-1544966503-7cc5ac882d5c?q=80&w=1000",
    title: "Termal Havuzlar",
    description: "Şifalı doğal termal sular"
  },
  {
    url: "https://images.unsplash.com/photo-1566073771259-6a8506099945?q=80&w=1000",
    title: "Spa & Wellness",
    description: "Hu<PERSON>r ve sağlık merkezi"
  },
  {
    url: "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?q=80&w=1000",
    title: "<PERSON><PERSON><PERSON><PERSON>",
    description: "<PERSON><PERSON><PERSON><PERSON> iç içe yüzme keyfi"
  },
  {
    url: "https://images.unsplash.com/photo-1540541338287-41700207dee6?q=80&w=1000",
    title: "Restoran",
    description: "Lezzetli ve sağlıklı yemekler"
  },
  {
    url: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=1000",
    title: "Doğal Çevre",
    description: "Yeşillikler içinde huzur"
  }
];

export const GallerySection = () => {
  return (
    <section id="gallery" className="py-20 px-4 bg-gradient-to-b from-secondary/30 to-background">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <span className="text-primary font-semibold text-sm uppercase tracking-wider animate-slide-up">Galeri</span>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 mt-2 animate-slide-in-left animate-delay-200">Tesisimizden Görüntüler</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto animate-slide-in-right animate-delay-300">
            Doğayla iç içe termal otelimizin güzelliklerini keşfedin
          </p>
        </div>

        {/* Featured large image */}
        <div className="mb-12 relative rounded-2xl overflow-hidden shadow-2xl animate-scale-in animate-delay-500">
          <img
            src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=1400"
            alt="Akasya Vadisi Termal Otel Ana Görünüm"
            className="w-full h-96 md:h-[500px] object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
          <div className="absolute bottom-8 left-8 text-white">
            <h3 className="text-3xl font-bold mb-2">Akasya Vadisi Termal Otel</h3>
            <p className="text-lg opacity-90">Doğanın kalbinde lüks ve konforu bir arada yaşayın</p>
          </div>
        </div>

        {/* Image carousel */}
        <div className="mb-12 animate-slide-in-left animate-delay-700">
          <Carousel className="w-full">
            <CarouselContent className="-ml-2 md:-ml-4">
              {galleryImages.map((image, index) => (
                <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                  <div className="relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group">
                    <img
                      src={image.url}
                      alt={image.title}
                      className="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-4 left-4 text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                        <p className="font-semibold text-lg">{image.title}</p>
                        <p className="text-sm opacity-90">{image.description}</p>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg" />
            <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg" />
          </Carousel>
        </div>

        {/* Grid layout for remaining images */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {galleryImages.slice(0, 6).map((image, index) => (
            <div key={index} className="relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group">
              <img
                src={image.url}
                alt={image.title}
                className="w-full h-64 object-cover transform group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 text-white">
                  <p className="font-semibold">{image.title}</p>
                  <p className="text-sm opacity-90">{image.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Button size="lg" variant="outline" className="hover:bg-primary hover:text-white transition-all">
            Tüm Fotoğrafları Gör
          </Button>
        </div>
      </div>
    </section>
  );
};