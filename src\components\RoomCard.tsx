import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RoomCarousel } from "./RoomCarousel";
import { Users, Wifi, Tv, Coffee, Car, Shield } from "lucide-react";

interface RoomCardProps {
  title: string;
  description: string;
  price: string;
  capacity: number;
  features: string[];
  images: string[];
}

export const RoomCard = ({ title, description, price, capacity, features, images }: RoomCardProps) => {
  const amenityIcons = [
    <Wifi className="h-4 w-4" />,
    <Tv className="h-4 w-4" />,
    <Coffee className="h-4 w-4" />,
    <Car className="h-4 w-4" />,
    <Shield className="h-4 w-4" />
  ];

  return (
    <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      <div className="relative">
        <RoomCarousel images={images} title={title} />
      </div>
      
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-2xl text-primary">{title}</CardTitle>
          <div className="flex items-center text-muted-foreground">
            <Users className="h-4 w-4 mr-1" />
            <span className="text-sm">{capacity} kişi</span>
          </div>
        </div>
        <CardDescription className="text-base">{description}</CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        
        
        <div className="space-y-3">
          <h4 className="font-semibold text-sm uppercase tracking-wider text-muted-foreground">Özellikler</h4>
          <ul className="space-y-2">
            {features.map((feature, index) => (
              <li key={index} className="flex items-start text-sm">
                <span className="text-primary mr-2">•</span>
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="space-y-3">
          <h4 className="font-semibold text-sm uppercase tracking-wider text-muted-foreground">Amenities</h4>
          <div className="flex items-center space-x-4">
            {amenityIcons.slice(0, 5).map((icon, index) => (
              <div key={index} className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full text-primary">
                {icon}
              </div>
            ))}
          </div>
        </div>
        
        <Button className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary text-white shadow-lg hover:shadow-xl transition-all">
          Rezervasyon Yap
        </Button>
      </CardContent>
    </Card>
  );
};