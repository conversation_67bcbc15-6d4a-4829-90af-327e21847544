import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Badge } from "@/components/ui/badge";
import { Calendar, MapPin, Users, Mountain } from "lucide-react";

const activityImages = [
  {
    url: "https://images.unsplash.com/photo-1551632811-561732d1e306?q=80&w=1400",
    alt: "<PERSON><PERSON><PERSON>"
  },
  {
    url: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=1400",
    alt: "Bisiklet Turları"
  },
  {
    url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=1400",
    alt: "Yoga Seansları"
  },
  {
    url: "https://images.unsplash.com/photo-1544966503-7cc5ac882d5c?q=80&w=1400",
    alt: "<PERSON><PERSON>ık Hava Aktiviteleri"
  }
];

const activities = [
  {
    icon: <Mountain className="h-6 w-6" />,
    title: "<PERSON><PERSON><PERSON>ü",
    description: "Eşsiz doğal güzelliklerde rehberli yürüyüş turları",
    time: "Her gün 09:00 & 16:00",
    duration: "2-3 saat",
    participants: "Maksimum 15 kişi"
  },
  {
    icon: <Users className="h-6 w-6" />,
    title: "Grup Yoga",
    description: "Termal havuz kenarında meditasyon ve yoga",
    time: "Her gün 07:00 & 18:00", 
    duration: "1 saat",
    participants: "Maksimum 20 kişi"
  },
  {
    icon: <Calendar className="h-6 w-6" />,
    title: "Fotoğraf Safari",
    description: "Profesyonel fotoğrafçı eşliğinde doğa fotoğrafçılığı",
    time: "Hafta sonu 14:00",
    duration: "3 saat",
    participants: "Maksimum 10 kişi"
  },
  {
    icon: <MapPin className="h-6 w-6" />,
    title: "Bisiklet Turları",
    description: "Çevredeki tarihi ve doğal yerleri keşfedin",
    time: "Her gün 10:00",
    duration: "2 saat",
    participants: "Maksimum 12 kişi"
  }
];

const weeklyProgram = [
  { day: "Pazartesi", morning: "Yoga (07:00)", afternoon: "Doğa Yürüyüşü (16:00)" },
  { day: "Salı", morning: "Bisiklet (10:00)", afternoon: "Termal Terapi (15:00)" },
  { day: "Çarşamba", morning: "Yoga (07:00)", afternoon: "Spa Workshop (14:00)" },
  { day: "Perşembe", morning: "Doğa Yürüyüşü (09:00)", afternoon: "Meditasyon (17:00)" },
  { day: "Cuma", morning: "Bisiklet (10:00)", afternoon: "Grup Aktivitesi (16:00)" },
  { day: "Cumartesi", morning: "Yoga (07:00)", afternoon: "Fotoğraf Safari (14:00)" },
  { day: "Pazar", morning: "Serbest", afternoon: "Doğa Yürüyüşü (16:00)" }
];

export const ActivitiesSection = () => {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-secondary/30 to-background">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Image Carousel Left */}
          <div className="relative order-2 lg:order-1 animate-slide-in-left animate-delay-300">
            <Carousel className="w-full">
              <CarouselContent>
                {activityImages.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                      <img
                        src={image.url}
                        alt={image.alt}
                        className="w-full h-96 md:h-[500px] object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
                      <div className="absolute bottom-6 left-6 text-white">
                        <p className="text-xl font-semibold drop-shadow-lg">{image.alt}</p>
                        <p className="text-sm opacity-90 drop-shadow-lg">Doğayla Bir Olun</p>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-0" />
              <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-0" />
            </Carousel>
          </div>

          {/* Content Right */}
          <div className="space-y-8 order-1 lg:order-2 animate-slide-in-right">
            <div className="space-y-4">
              <Badge variant="outline" className="text-primary border-primary animate-slide-up">
                Aktiviteler
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold text-foreground animate-slide-in-right animate-delay-200">
                Doğayla İç İçe
                <span className="text-primary block">Deneyimler</span>
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed animate-slide-in-right animate-delay-300">
                Termal tedavinizin yanında doğanın içinde unutulmaz anlar yaşayın. 
                Uzman rehberlerimiz eşliğinde düzenlenen aktivitelerle hem eğlenin hem sağlığınıza katkıda bulunun.
              </p>
            </div>

            {/* Activities Grid */}
            <div className="space-y-4">
              {activities.map((activity, index) => (
                <div key={index} className="bg-card p-6 rounded-xl border shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-primary/10 rounded-lg text-primary">
                      {activity.icon}
                    </div>
                    <div className="flex-1 space-y-2">
                      <h3 className="text-lg font-semibold text-card-foreground">{activity.title}</h3>
                      <p className="text-sm text-muted-foreground">{activity.description}</p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-primary">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{activity.time}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{activity.duration}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-3 w-3" />
                          <span>{activity.participants}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Weekly Program */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-foreground">Haftalık Program</h3>
              <div className="bg-card p-6 rounded-xl border">
                <div className="space-y-3">
                  {weeklyProgram.map((program, index) => (
                    <div key={index} className="flex flex-col md:flex-row md:items-center justify-between p-3 rounded-lg bg-secondary/30">
                      <span className="font-medium text-card-foreground mb-2 md:mb-0">{program.day}</span>
                      <div className="flex flex-col md:flex-row md:space-x-4 space-y-1 md:space-y-0">
                        <span className="text-sm text-muted-foreground">{program.morning}</span>
                        <span className="text-sm text-muted-foreground">{program.afternoon}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Info Card */}
            <div className="bg-primary/5 p-6 rounded-xl border border-primary/20">
              <h3 className="text-lg font-semibold text-foreground mb-3">Katılım Bilgileri</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Tüm aktiviteler ücretsizdir</li>
                <li>• Rezervasyon resepsiyondan yapılabilir</li>
                <li>• Hava durumuna göre program değişebilir</li>
                <li>• Spor kıyafeti önerilir</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};