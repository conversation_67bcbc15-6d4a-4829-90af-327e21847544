import { useIsMobile } from "@/hooks/use-mobile";
import { useEffect } from "react";
import { Header } from "@/components/Header";
import { HeroSection } from "@/components/HeroSection";
import { AboutSection } from "@/components/AboutSection";
import { RoomCard } from "@/components/RoomCard";
import { RestaurantSection } from "@/components/RestaurantSection";
import { ThermalPoolSection } from "@/components/ThermalPoolSection";
import { SpaWellnessSection } from "@/components/SpaWellnessSection";
import { ActivitiesSection } from "@/components/ActivitiesSection";
import { GallerySection } from "@/components/GallerySection";
import { ContactSection } from "@/components/ContactSection";
import { Button } from "@/components/ui/button";
import { Phone, MessageCircle } from "lucide-react";

const Index = () => {
  const isMobile = useIsMobile();

  const handleCall = () => {
    window.open("tel:+903125943000", "_self");
    // Track conversion
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'conversion', {
        'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL'
      });
    }
    if (typeof window !== 'undefined' && (window as any).fbq) {
      (window as any).fbq('track', 'Contact');
    }
  };

  const handleWhatsApp = () => {
    window.open("https://wa.me/905323456789", "_blank");
    // Track conversion
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'conversion', {
        'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL'
      });
    }
    if (typeof window !== 'undefined' && (window as any).fbq) {
      (window as any).fbq('track', 'Contact');
    }
  };

  const handleReservation = () => {
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'conversion', {
        'send_to': 'AW-CONVERSION_ID/RESERVATION_LABEL'
      });
    }
    if (typeof window !== 'undefined' && (window as any).fbq) {
      (window as any).fbq('track', 'InitiateCheckout');
    }
  };

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    // Page view tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', 'GA_MEASUREMENT_ID');
    }
    if (typeof window !== 'undefined' && (window as any).fbq) {
      (window as any).fbq('track', 'PageView');
    }
  }, []);

  // Room data with images
  const roomData = [
    {
      title: "Standart Oda (1+1)",
      description: "4 kişi kapasiteli VIP daire",
      price: "5.000 TL",
      capacity: 4,
      features: [
        "Oturma alanı, yatak odası, mutfak, banyo",
        "LCD TV, uydu kanalları, WiFi",
        "Mutfak eşyaları, buzdolabı, ocak",
        "Termal su kullanımı (08:00-10:00, 20:00-22:00)",
        "Saç kurutma makinesi, banyo malzemeleri"
      ],
      images: [
        "/images/odalar/oda-1.jpg",
        "/images/odalar/oda-2.jpg",
        "/images/odalar/oda-3.jpg",
        "/images/odalar/oda-4.jpg"
      ]
    },
    {
      title: "Aile Odası (3+1)",
      description: "6 kişi kapasiteli lüks daire",
      price: "8.750 TL",
      capacity: 6,
      features: [
        "Geniş oturma alanı, 2 yatak odası, mutfak, banyo",
        "LCD TV, uydu kanalları, WiFi",
        "Tam donanımlı mutfak, buzdolabı, ocak",
        "Termal su kullanımı (08:00-10:00, 20:00-22:00)",
        "Aile için ideal, geniş balkon"
      ],
      images: [
        "/images/odalar/oda-6.jpg",
        "/images/odalar/oda-8.jpg",
        "/images/odalar/oda-9.jpg",
        "/images/odalar/oda-10.jpeg"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header 
        onCall={handleCall}
        onReservation={handleReservation}
        scrollToSection={scrollToSection}
      />

      <HeroSection 
        onCall={handleCall}
        onReservation={handleReservation}
      />

      <AboutSection />

      {/* Rooms Section */}
      <section id="rooms" className="py-20 px-4 bg-gradient-to-b from-background to-secondary/50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16 animate-fade-in">
            <span className="text-primary font-semibold text-sm uppercase tracking-wider animate-slide-up">Odalar</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 mt-2 animate-slide-in-left animate-delay-200">Lüks Konaklama Seçenekleri</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto animate-slide-in-right animate-delay-300">
              Tamamı VIP olarak hazırlanmış, modern ve konforlu odalarımızda misafirlerimize unutulmaz bir deneyim sunuyoruz
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {roomData.map((room, index) => (
              <div key={index} className={`${index % 2 === 0 ? 'animate-slide-in-left' : 'animate-slide-in-right'} animate-delay-500`}>
                <RoomCard
                  title={room.title}
                  description={room.description}
                  price={room.price}
                  capacity={room.capacity}
                  features={room.features}
                  images={room.images}
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      <RestaurantSection />

      <ThermalPoolSection />

      <SpaWellnessSection />

      <ActivitiesSection />

      <GallerySection />

      <ContactSection 
        onCall={handleCall}
        onWhatsApp={handleWhatsApp}
      />

      {/* Mobile Fixed Buttons */}
      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-border shadow-lg">
          <div className="grid grid-cols-2 gap-0">
            <Button 
              onClick={handleCall}
              className="rounded-none h-14 text-white bg-primary hover:bg-primary/90"
              size="lg"
            >
              <Phone className="h-5 w-5 mr-2" />
              Ara
            </Button>
            <Button 
              onClick={handleWhatsApp}
              className="rounded-none h-14 text-white bg-green-600 hover:bg-green-700"
              size="lg"
            >
              <MessageCircle className="h-5 w-5 mr-2" />
              WhatsApp
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Index;