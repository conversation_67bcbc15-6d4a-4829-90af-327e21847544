import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Phone, MessageCircle, MapPin, Mail, Clock, Navigation } from "lucide-react";

interface ContactSectionProps {
  onCall: () => void;
  onWhatsApp: () => void;
}

export const ContactSection = ({ onCall, onWhatsApp }: ContactSectionProps) => {
  return (
    <section id="contact" className="py-20 px-4 bg-gradient-to-br from-primary/5 to-secondary/20">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <span className="text-primary font-semibold text-sm uppercase tracking-wider animate-slide-up"><PERSON>letişim</span>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 mt-2 animate-slide-in-left animate-delay-200"><PERSON><PERSON><PERSON><PERSON> Geçin</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto animate-slide-in-right animate-delay-300">
            Rezervasyon yapmak veya bilgi almak için bizimle iletişime geçebilirsiniz
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 items-start">
          {/* Contact Info */}
          <div className="space-y-6 animate-slide-in-left animate-delay-500">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <Phone className="h-6 w-6 text-primary mr-3" />
                <CardTitle className="text-lg">Telefon</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-primary mb-2">+90 312 594 30 00</p>
                <p className="text-muted-foreground">7/24 Rezervasyon Hattı</p>
                <Button onClick={onCall} className="mt-3 w-full">
                  <Phone className="h-4 w-4 mr-2" />
                  Hemen Ara
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <MessageCircle className="h-6 w-6 text-primary mr-3" />
                <CardTitle className="text-lg">WhatsApp</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xl font-semibold mb-2">+90 532 345 67 89</p>
                <p className="text-muted-foreground">Hızlı iletişim için</p>
                <Button onClick={onWhatsApp} variant="outline" className="mt-3 w-full">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  WhatsApp
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <Mail className="h-6 w-6 text-primary mr-3" />
                <CardTitle className="text-lg">E-posta</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg font-semibold mb-2"><EMAIL></p>
                <p className="text-muted-foreground">Detaylı bilgi için</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <Clock className="h-6 w-6 text-primary mr-3" />
                <CardTitle className="text-lg">Çalışma Saatleri</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Rezervasyon:</span>
                    <span className="font-semibold">7/24</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Resepsiyon:</span>
                    <span className="font-semibold">7/24</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Check-in:</span>
                    <span className="font-semibold">14:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Check-out:</span>
                    <span className="font-semibold">12:00</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Map and Location */}
          <div className="space-y-6 animate-slide-in-right animate-delay-700">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <MapPin className="h-6 w-6 text-primary mr-3" />
                <CardTitle className="text-lg">Adres</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="font-semibold mb-2">Kızılcahamam Termal Turizm Merkezi</p>
                <p className="text-muted-foreground mb-4">
                  Kızılcahamam, Ankara, Türkiye
                </p>
                <Button variant="outline" className="w-full">
                  <Navigation className="h-4 w-4 mr-2" />
                  Yol Tarifi Al
                </Button>
              </CardContent>
            </Card>

            {/* Map placeholder */}
            <div className="relative h-64 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl overflow-hidden shadow-lg">
              <img
                src="https://images.unsplash.com/photo-1569336415962-a4bd9f69cd83?q=80&w=1000"
                alt="Harita Konumu"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                <div className="text-center text-white">
                  <MapPin className="h-12 w-12 mx-auto mb-2" />
                  <p className="font-semibold">Akasya Vadisi Termal Otel</p>
                  <p className="text-sm opacity-90">Kızılcahamam, Ankara</p>
                </div>
              </div>
            </div>

            {/* Distance info */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-white rounded-lg shadow">
                <p className="text-2xl font-bold text-primary">40 dk</p>
                <p className="text-sm text-muted-foreground">Ankara'ya</p>
              </div>
              <div className="text-center p-4 bg-white rounded-lg shadow">
                <p className="text-2xl font-bold text-primary">60 dk</p>
                <p className="text-sm text-muted-foreground">Havalimanına</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};