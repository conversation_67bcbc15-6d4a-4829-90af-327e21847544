import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Badge } from "@/components/ui/badge";
import { Thermometer, Droplets, Heart, Shield } from "lucide-react";

const thermalImages = [
  {
    url: "/images/havuzlar/havuz-1.jpg",
    alt: "Ana Termal Havuz"
  },
  {
    url: "/images/havuzlar/havuz-2.jpg",
    alt: "Jakuzi ve Sıcak Su Terapisi"
  },
  {
    url: "/images/havuzlar/havuz-3.jpg",
    alt: "Açık Havuz Alanı"
  },
  {
    url: "/images/havuzlar/havuz-4.jpg",
    alt: "Termal Su Kaynakları"
  }
];

const thermalBenefits = [
  {
    icon: <Thermometer className="h-6 w-6" />,
    title: "Sıcaklık Kontrolü",
    description: "38-42°C arası optimum sıcaklık",
    detail: "Vücut sıcaklığına uygun termal su"
  },
  {
    icon: <Droplets className="h-6 w-6" />,
    title: "Mineral Zenginliği",
    description: "Doğal mineral içeriği",
    detail: "Kükürt, magnezyum ve kalsiyum"
  },
  {
    icon: <Heart className="h-6 w-6" />,
    title: "Sağlık Faydaları",
    description: "Romatizma ve eklem ağrıları",
    detail: "Dolaşım sistemini güçlendirir"
  },
  {
    icon: <Shield className="h-6 w-6" />,
    title: "Cilt Bakımı",
    description: "Doğal cilt yenileme",
    detail: "Anti-aging ve detoks etkisi"
  }
];

const poolFeatures = [
  "24 Saat Açık Termal Havuzlar",
  "Farklı Sıcaklık Seçenekleri",
  "Çocuk Dostu Sığ Havuzlar", 
  "Jakuzi ve Hidromasaj",
  "Soyunma Odaları ve Duşlar",
  "Havuz Başı Servis"
];

export const ThermalPoolSection = () => {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-secondary/30 to-background">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Image Carousel Left */}
          <div className="relative order-2 lg:order-1 animate-slide-in-left animate-delay-300">
            <Carousel className="w-full">
              <CarouselContent>
                {thermalImages.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                      <img
                        src={image.url}
                        alt={image.alt}
                        className="w-full h-96 md:h-[500px] object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
                      <div className="absolute bottom-6 left-6 text-white">
                        <p className="text-xl font-semibold drop-shadow-lg">{image.alt}</p>
                        <p className="text-sm opacity-90 drop-shadow-lg">Doğal Termal Kaynaklar</p>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-0" />
              <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-0" />
            </Carousel>
          </div>

          {/* Content Right */}
          <div className="space-y-8 order-1 lg:order-2 animate-slide-in-right">
            <div className="space-y-4">
              <Badge variant="outline" className="text-primary border-primary animate-slide-up">
                Termal Havuzlar
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold text-foreground animate-slide-in-right animate-delay-200">
                Şifalı Termal 
                <span className="text-primary block">Sularımız</span>
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed animate-slide-in-right animate-delay-300">
                Binlerce yıldır yeraltından kaynayan doğal termal sularımız, 
                sağlık ve wellness arayışınızda size eşlik ediyor. Mineral açısından 
                zengin sularımız hem vücudunuzu hem de ruhunuzu yeniliyor.
              </p>
            </div>

            {/* Benefits Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {thermalBenefits.map((benefit, index) => (
                <div key={index} className="bg-card p-6 rounded-xl border shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-primary/10 rounded-lg text-primary">
                      {benefit.icon}
                    </div>
                    <div className="space-y-2">
                      <h3 className="font-semibold text-card-foreground">{benefit.title}</h3>
                      <p className="text-sm text-muted-foreground">{benefit.description}</p>
                      <p className="text-xs text-primary font-medium">{benefit.detail}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

           
            {/* Temperature Info */}
            <div className="bg-primary/5 p-6 rounded-xl border border-primary/20">
              <div className="flex items-center space-x-3 mb-4">
                <Thermometer className="h-6 w-6 text-primary" />
                <h3 className="text-lg font-semibold text-foreground">Sıcaklık Bilgisi</h3>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="space-y-1">
                  <p className="text-2xl font-bold text-primary">38°C</p>
                  <p className="text-sm text-muted-foreground">Çocuk Havuzu</p>
                </div>
                <div className="space-y-1">
                  <p className="text-2xl font-bold text-primary">40°C</p>
                  <p className="text-sm text-muted-foreground">Ana Havuz</p>
                </div>
                <div className="space-y-1">
                  <p className="text-2xl font-bold text-primary">42°C</p>
                  <p className="text-sm text-muted-foreground">Jakuzi</p>
                </div>
                <div className="space-y-1">
                  <p className="text-2xl font-bold text-primary">36°C</p>
                  <p className="text-sm text-muted-foreground">Soğuk Havuz</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};