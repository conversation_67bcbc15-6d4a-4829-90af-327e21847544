# A<PERSON>ya Vadisi Termal Otel Website

Modern ve responsive web sitesi - React, TypeScript ve Tailwind CSS ile geliştirilmiştir.

## 🏨 <PERSON>je Hakkında

Ankara Kızılcahamam'da bulunan Akasya Vadisi Termal Otel'in resmi web sitesi. 124 o<PERSON>, al<PERSON><PERSON><PERSON><PERSON> herşey dahil konseptinde premium termal otel deneyimi sunan otelin tanıtım sitesidir.

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/ffff453d-6b32-4abe-a691-ba6b00bb0f95) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## 🚀 Caprover ile Deployment

Bu proje Caprover ile deploy edilmek üzere hazırlanmıştır.

### Gerekli Dosyalar:
- `Dockerfile` - Multi-stage build ile optimize edilmiş
- `nginx.conf` - Production nginx konfigürasyonu
- `captain-definition` - Caprover deployment tanımı
- `.dockerignore` - Docker build optimizasyonu

### Deployment Adımları:

1. **Projeyi zip olarak hazırlayın:**
```bash
# Gereksiz dosyaları hariç tutarak zip oluşturun
zip -r akasya-vadisi-website.zip . -x "node_modules/*" ".git/*" "dist/*" "*.log"
```

2. **Caprover'da uygulama oluşturun:**
   - Caprover dashboard'una giriş yapın
   - "Apps" sekmesine gidin
   - "Create New App" butonuna tıklayın
   - App adını girin (örn: `akasya-vadisi-website`)

3. **Deployment yapın:**
   - Oluşturulan app'e tıklayın
   - "Deployment" sekmesine gidin
   - Zip dosyasını upload edin
   - Deploy butonuna tıklayın

4. **Domain ayarları:**
   - "HTTP Settings" sekmesine gidin
   - Custom domain ekleyin: `www.akasyavadisitermal.com`
   - SSL sertifikası etkinleştirin

### Environment Variables (Opsiyonel):
```
VITE_APP_TITLE=Akasya Vadisi Termal Otel
VITE_APP_URL=https://www.akasyavadisitermal.com
VITE_PHONE_NUMBER=+************
VITE_EMAIL=<EMAIL>
```

### Build Optimizasyonları:
- Gzip compression aktif
- Static asset caching (1 yıl)
- Security headers
- Client-side routing desteği
- Chunk splitting (vendor, ui)
