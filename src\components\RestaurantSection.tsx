import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, ChefHat } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const restaurantImages = [
  {
    url: "/images/restoran/restoran-1.jpg",
    alt: "Ana Restaurant Salonu"
  },
  {
    url: "/images/restoran/restoran-2.jpg",
    alt: "Açık Hava Terası"
  },
  {
    url: "/images/restoran/restoran-3.jpg",
    alt: "Özel Yemek Sunumu"
  },
  {
    url: "/images/genel/photo-15215-B74OHsUKA9omMNg.jpg",
    alt: "Şef Mutfağı"
  }
];

const specialties = [
  "Geleneksel Türk Mutfağı",
  "Organik Sebze Yemekleri", 
  "<PERSON>ze Balık ve Deniz Ürünleri",
  "Özel Diyet Menüleri",
  "Çocuk Dostu Menüler"
];

export const RestaurantSection = () => {
  const contentAnimation = useScrollAnimation();
  const imageAnimation = useScrollAnimation();

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-background to-secondary/30">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content Left */}
          <div ref={contentAnimation.ref} className={`space-y-8 transition-all duration-1000 ${contentAnimation.isVisible ? 'animate-slide-in-left' : 'opacity-0 -translate-x-10'}`}>
            <div className="space-y-4">
              <Badge variant="outline" className={`text-primary border-primary transition-all duration-700 ${contentAnimation.isVisible ? 'animate-slide-up' : 'opacity-0 translate-y-5'}`}>
                Gastronomi
              </Badge>
              <h2 className={`text-4xl md:text-5xl font-bold text-foreground transition-all duration-1000 delay-200 ${contentAnimation.isVisible ? 'animate-slide-in-left' : 'opacity-0 -translate-x-10'}`}>
                Lezzet Yolculuğunuz 
                <span className="text-primary block">Başlıyor</span>
              </h2>
              <p className={`text-lg text-muted-foreground leading-relaxed transition-all duration-1000 delay-300 ${contentAnimation.isVisible ? 'animate-slide-in-left' : 'opacity-0 -translate-x-5'}`}>
                Deneyimli şeflerimizin hazırladığı özel yemeklerle damak tadınızı tatmin edin. 
                Taze, organik malzemelerle hazırlanan geleneksel ve modern lezzetlerin buluştuğu 
                eşsiz bir gastronomi deneyimi sizi bekliyor.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-card p-6 rounded-xl border shadow-sm">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Clock className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="font-semibold text-card-foreground">Servis Saatleri</h3>
                </div>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Kahvaltı:</span>
                    <span>07:00 - 10:30</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Öğle:</span>
                    <span>12:00 - 15:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Akşam:</span>
                    <span>19:00 - 22:30</span>
                  </div>
                </div>
              </div>

              <div className="bg-card p-6 rounded-xl border shadow-sm">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="font-semibold text-card-foreground">Kapasite</h3>
                </div>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex justify-between">
                    <span>İç Mekan:</span>
                    <span>150 Kişi</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Teras:</span>
                    <span>80 Kişi</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Özel Salon:</span>
                    <span>40 Kişi</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <ChefHat className="h-5 w-5 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-foreground">Özel Lezzetlerimiz</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {specialties.map((specialty, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-secondary/50 rounded-lg">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-secondary-foreground">{specialty}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Image Carousel Right */}
          <div ref={imageAnimation.ref} className={`relative transition-all duration-1000 delay-300 ${imageAnimation.isVisible ? 'animate-slide-in-right' : 'opacity-0 translate-x-10'}`}>
            <Carousel className="w-full">
              <CarouselContent>
                {restaurantImages.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                      <img
                        src={image.url}
                        alt={image.alt}
                        className="w-full h-96 md:h-[500px] object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
                      <div className="absolute bottom-6 left-6 text-white">
                        <p className="text-xl font-semibold drop-shadow-lg">{image.alt}</p>
                        <p className="text-sm opacity-90 drop-shadow-lg">Akasya Vadisi Restaurant</p>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-0" />
              <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-0" />
            </Carousel>
          </div>
        </div>
      </div>
    </section>
  );
};