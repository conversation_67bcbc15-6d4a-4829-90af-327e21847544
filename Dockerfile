# Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage - Simple static file server
FROM node:18-alpine

# Install serve globally
RUN npm install -g serve

# Copy built files
COPY --from=builder /app/dist /app

# Set working directory
WORKDIR /app

# Expose port 80
EXPOSE 80

# Start serve
CMD ["serve", "-s", ".", "-l", "80"]
