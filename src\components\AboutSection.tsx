import { Badge } from "@/components/ui/badge";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Leaf, MapPin, Users, Heart, Award, Shield } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const aboutImages = [
  {
    url: "/images/genel/photo-15215-INmgwzDQqdJpKF3.jpg",
    alt: "Akasya Vadisi Termal Otel Ana Bina"
  },
  {
    url: "/images/havuzlar/havuz-3.jpg",
    alt: "Termal Havuz Alanları"
  },
  {
    url: "/images/genel/photo-15215-P7pWlAzekRq4BFb.jpg",
    alt: "Doğal Çevre ve Manzara"
  },
  {
    url: "/images/havuzlar/havuz-4.jpg",
    alt: "Spa ve Wellness Merkezi"
  }
];

const features = [
  {
    icon: <Leaf className="h-6 w-6" />,
    title: "Doğal Termal Su",
    description: "%100 doğal, yeraltı kaynaklarından"
  },
  {
    icon: <MapPin className="h-6 w-6" />,
    title: "Eşsiz Konum",
    description: "Doğanın kalbinde, şehirden uzak"
  },
  {
    icon: <Users className="h-6 w-6" />,
    title: "Aile Dostu",
    description: "Her yaştan misafir için uygun"
  },
  {
    icon: <Heart className="h-6 w-6" />,
    title: "Sağlık Odaklı",
    description: "Wellness ve terapi programları"
  },
  {
    icon: <Award className="h-6 w-6" />,
    title: "25 Yıl Deneyim",
    description: "Termal turizm alanında uzman"
  },
  {
    icon: <Shield className="h-6 w-6" />,
    title: "Güvenli Ortam",
    description: "7/24 güvenlik ve sağlık hizmetleri"
  }
];

export const AboutSection = () => {
  const titleAnimation = useScrollAnimation();
  const contentAnimation = useScrollAnimation();
  const imageAnimation = useScrollAnimation();

  return (
    <section id="about" className="py-20 px-4 bg-gradient-to-b from-secondary/30 to-background">
      <div className="max-w-7xl mx-auto">
        <div ref={titleAnimation.ref} className={`text-center mb-16 transition-all duration-1000 ${titleAnimation.isVisible ? 'animate-fade-in' : 'opacity-0 translate-y-10'}`}>
          <Badge variant="outline" className={`text-primary border-primary mb-4 transition-all duration-700 ${titleAnimation.isVisible ? 'animate-slide-up' : 'opacity-0 translate-y-5'}`}>
            Hakkımızda
          </Badge>
          <h2 className={`text-4xl md:text-5xl font-bold mb-6 transition-all duration-1000 delay-200 ${titleAnimation.isVisible ? 'animate-slide-in-left' : 'opacity-0 -translate-x-10'}`}>
            Akasya Vadisi <span className="text-primary">Termal Otel</span>
          </h2>
          <p className={`text-lg text-muted-foreground max-w-3xl mx-auto transition-all duration-1000 delay-300 ${titleAnimation.isVisible ? 'animate-slide-in-right' : 'opacity-0 translate-x-10'}`}>
            1998 yılından bu yana, doğanın kalbinde yer alan tesisimizde misafirlerimize 
            sağlık, huzur ve konforun mükemmel birleşimini sunuyoruz.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Content Left */}
          <div ref={contentAnimation.ref} className={`space-y-8 transition-all duration-1000 delay-300 ${contentAnimation.isVisible ? 'animate-slide-in-left' : 'opacity-0 -translate-x-10'}`}>
            <div className="space-y-4">
              <h3 className={`text-3xl font-bold text-foreground transition-all duration-1000 delay-500 ${contentAnimation.isVisible ? 'animate-slide-in-left' : 'opacity-0 -translate-x-5'}`}>Doğal Termal Kaynaklar ve Modern Konfor</h3>
              <p className="text-muted-foreground leading-relaxed">
                Binlerce yıldır yeraltından kaynayan doğal termal sularımız, modern tesis 
                imkanlarımızla birleşerek misafirlerimize eşsiz bir sağlık ve dinlenme deneyimi sunuyor. 
                38-42°C arası sıcaklıktaki mineral açısından zengin sularımız, romatizma, eklem ağrıları 
                ve cilt problemleri için doğal çözümler sunar.
              </p>
              <p className="text-muted-foreground leading-relaxed">
                25 yıllık deneyimimizle, termal turizm alanında Türkiye'nin önde gelen tesislerinden 
                biri olmanın gururunu yaşıyoruz. Misafir memnuniyeti odaklı hizmet anlayışımızla, 
                her yıl binlerce yerli ve yabancı turisti ağırlıyoruz.
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <div key={index} className="bg-card p-4 rounded-xl border shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg text-primary">
                      {feature.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground">{feature.title}</h4>
                      <p className="text-sm text-muted-foreground">{feature.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Image Grid Right */}
          <div ref={imageAnimation.ref} className={`grid grid-cols-2 gap-4 transition-all duration-1000 delay-500 ${imageAnimation.isVisible ? 'animate-slide-in-right' : 'opacity-0 translate-x-10'}`}>
            {aboutImages.map((image, index) => (
              <div key={index} className="relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group">
                <img
                  src={image.url}
                  alt={image.alt}
                  className="w-full h-48 md:h-56 object-cover transform group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute bottom-3 left-3 text-white transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                  <p className="text-sm font-semibold drop-shadow-lg">{image.alt}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 bg-primary/5 p-8 rounded-2xl border border-primary/20">
          <div className="text-center">
            <p className="text-3xl md:text-4xl font-bold text-primary">25+</p>
            <p className="text-sm text-muted-foreground">Yıl Deneyim</p>
          </div>
          <div className="text-center">
            <p className="text-3xl md:text-4xl font-bold text-primary">50K+</p>
            <p className="text-sm text-muted-foreground">Mutlu Misafir</p>
          </div>
          <div className="text-center">
            <p className="text-3xl md:text-4xl font-bold text-primary">40°C</p>
            <p className="text-sm text-muted-foreground">Ortalama Su Sıcaklığı</p>
          </div>
          <div className="text-center">
            <p className="text-3xl md:text-4xl font-bold text-primary">365</p>
            <p className="text-sm text-muted-foreground">Gün Açık</p>
          </div>
        </div>
      </div>
    </section>
  );
};