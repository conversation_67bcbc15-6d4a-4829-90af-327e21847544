import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Spark<PERSON>, Clock, Star, Leaf } from "lucide-react";

const spaImages = [
  {
    url: "/images/spasauna/spa-1.jpg",
    alt: "Masaj Odaları"
  },
  {
    url: "/images/spasauna/spa-2.jpg",
    alt: "Sauna ve Buhar Odası"
  },
  {
    url: "/images/spasauna/spa-3.jpg",
    alt: "<PERSON>len<PERSON> Alanı"
  },
  {
    url: "/images/spasauna/spa-4.jpg",
    alt: "Aromaterapi Odası"
  }
];

const spaServices = [
  {
    icon: <Sparkles className="h-5 w-5" />,
    name: "Klasik Masaj",
    duration: "60 dk",
    price: "850 TL",
    description: "Geleneksel İsveç masajı tekniği"
  },
  {
    icon: <Leaf className="h-5 w-5" />,
    name: "Aromaterapi",
    duration: "75 dk", 
    price: "950 TL",
    description: "Doğal yağlarla rahatlama"
  },
  {
    icon: <Star className="h-5 w-5" />,
    name: "Hot Stone",
    duration: "90 dk",
    price: "1.150 TL",
    description: "Sıcak taş terapisi"
  },
  {
    icon: <Sparkles className="h-5 w-5" />,
    name: "Hamam Keyfi",
    duration: "45 dk",
    price: "650 TL",
    description: "Geleneksel hamam deneyimi"
  }
];

const facilities = [
  "Profesyonel Masaj Odaları",
  "Finlandiya Saunası",
  "Türk Hamamı",
  "Buhar Odası",
  "Dinlenme Salonu",
  "Aromaterapi Merkezi",
  "Cilt Bakım Odaları",
  "Meditasyon Alanı"
];

export const SpaWellnessSection = () => {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-background to-secondary/30">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <Badge variant="outline" className="text-primary border-primary mb-4 animate-slide-up">
            Spa & Wellness
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 animate-slide-in-left animate-delay-200">
            Kendinizi <span className="text-primary">Şımartın</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto animate-slide-in-right animate-delay-300">
            Uzman terapistlerimiz eşliğinde, doğal ürünlerle hazırlanan özel bakım ve masaj hizmetleriyle 
            ruh ve beden dengenizi yeniden kurun.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Content Left */}
          <div className="space-y-8 animate-slide-in-left animate-delay-300">
            {/* Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {spaServices.map((service, index) => (
                <div key={index} className="bg-card p-6 rounded-xl border shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-start justify-between mb-4">
                    <div className="p-2 bg-primary/10 rounded-lg text-primary">
                      {service.icon}
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-primary">{service.price}</p>
                      <p className="text-sm text-muted-foreground flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {service.duration}
                      </p>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-card-foreground mb-2">{service.name}</h3>
                  <p className="text-sm text-muted-foreground">{service.description}</p>
                </div>
              ))}
            </div>

            {/* Facilities */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-foreground">Spa Tesislerimiz</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {facilities.map((facility, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-secondary/50 rounded-lg">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-secondary-foreground">{facility}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Special Offer */}
            <div className="bg-primary/5 p-6 rounded-xl border border-primary/20">
              <div className="flex items-center space-x-3 mb-4">
                <Star className="h-6 w-6 text-primary" />
                <h3 className="text-lg font-semibold text-foreground">Özel Paket Fırsatı</h3>
              </div>
              <p className="text-muted-foreground mb-4">
                2 gece konaklama + Spa paketi ile %20 indirim kazanın
              </p>
              <Button className="w-full md:w-auto">
                Paket Detayları
              </Button>
            </div>
          </div>

          {/* Image Carousel Right */}
          <div className="relative animate-slide-in-right animate-delay-500">
            <Carousel className="w-full">
              <CarouselContent>
                {spaImages.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                      <img
                        src={image.url}
                        alt={image.alt}
                        className="w-full h-96 md:h-[500px] object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
                      <div className="absolute bottom-6 left-6 text-white">
                        <p className="text-xl font-semibold drop-shadow-lg">{image.alt}</p>
                        <p className="text-sm opacity-90 drop-shadow-lg">Spa & Wellness Merkezi</p>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-0" />
              <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg border-0" />
            </Carousel>
          </div>
        </div>
      </div>
    </section>
  );
};