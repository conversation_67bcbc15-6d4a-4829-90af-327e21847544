import { But<PERSON> } from "@/components/ui/button";
import { Phone, Menu, X, Waves, Calendar } from "lucide-react";
import { useState } from "react";

interface HeaderProps {
  onCall: () => void;
  onReservation: () => void;
  scrollToSection: (sectionId: string) => void;
}

export const Header = ({ onCall, onReservation, scrollToSection }: HeaderProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const handleNavClick = (sectionId: string) => {
    scrollToSection(sectionId);
    setIsMenuOpen(false);
  };

  return (
    <header className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-border z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Waves className="h-8 w-8 text-primary mr-2" />
            <span className="font-bold text-xl text-primary">Akasya Vadisi</span>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <button onClick={() => handleNavClick('home')} className="text-foreground hover:text-primary transition-colors">Ana Sayfa</button>
            <button onClick={() => handleNavClick('about')} className="text-foreground hover:text-primary transition-colors">Hakkımızda</button>
            <button onClick={() => handleNavClick('rooms')} className="text-foreground hover:text-primary transition-colors">Odalar</button>
            <button onClick={() => handleNavClick('gallery')} className="text-foreground hover:text-primary transition-colors">Galeri</button>
            <button onClick={() => handleNavClick('contact')} className="text-foreground hover:text-primary transition-colors">İletişim</button>
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            <Button onClick={onCall} variant="outline" size="sm">
              <Phone className="h-4 w-4 mr-2" />
              Ara
            </Button>
            <Button onClick={onReservation} size="sm">
              Rezervasyon
            </Button>
          </div>

          {/* Mobile menu button */}
          <button onClick={toggleMenu} className="md:hidden p-2">
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-border bg-white">
            <nav className="py-4 space-y-4">
              <button onClick={() => handleNavClick('home')} className="block w-full text-left px-4 py-2 hover:bg-accent rounded">Ana Sayfa</button>
              <button onClick={() => handleNavClick('about')} className="block w-full text-left px-4 py-2 hover:bg-accent rounded">Hakkımızda</button>
              <button onClick={() => handleNavClick('rooms')} className="block w-full text-left px-4 py-2 hover:bg-accent rounded">Odalar</button>
              <button onClick={() => handleNavClick('gallery')} className="block w-full text-left px-4 py-2 hover:bg-accent rounded">Galeri</button>
              <button onClick={() => handleNavClick('contact')} className="block w-full text-left px-4 py-2 hover:bg-accent rounded">İletişim</button>
              <div className="px-4 space-y-2">
                <Button onClick={onCall} variant="outline" className="w-full">
                  <Phone className="h-4 w-4 mr-2" />
                  Ara
                </Button>
                <Button onClick={onReservation} className="w-full">
                  Rezervasyon
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};