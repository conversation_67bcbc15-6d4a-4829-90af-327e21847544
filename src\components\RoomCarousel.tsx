import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";

interface RoomCarouselProps {
  images: string[];
  title: string;
}

export const RoomCarousel = ({ images, title }: RoomCarouselProps) => {
  return (
    <div className="relative">
      <Carousel className="w-full">
        <CarouselContent>
          {images.map((image, index) => (
            <CarouselItem key={index}>
              <Card className="border-0 shadow-lg overflow-hidden">
                <CardContent className="p-0 relative">
                  <img
                    src={image}
                    alt={`${title} - Görüntü ${index + 1}`}
                    className="w-full h-64 md:h-80 object-cover transition-transform duration-300 hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <p className="font-semibold text-lg drop-shadow-md">{title}</p>
                    <p className="text-sm opacity-90 drop-shadow-md">Lüks ve konfor bir arada</p>
                  </div>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg" />
        <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg" />
      </Carousel>
    </div>
  );
};